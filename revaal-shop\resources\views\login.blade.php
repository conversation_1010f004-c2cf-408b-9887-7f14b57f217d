@extends('app')
@section('title', 'ورود')
@section('style')
<link rel="stylesheet" type="text/css" href="{{ asset('css/demo4.min.css') }}">
<link rel="stylesheet" type="text/css" href="{{ asset('css/style.min.css') }}">
@endsection
@section('content')
<main class="main rtl text-right">
            <nav class="breadcrumb-nav">
                <div class="container">
                    <ul class="breadcrumb">
                        <li><a href="demo1.html"><i class="d-icon-home"></i></a></li>
                        <li><a href="shop.html">فروشگاه</a></li>
                        <li> حساب کاربری </li>
                    </ul>
                </div>
            </nav>
            <div class="lgoinnn page-content mt-6 pb-2 mb-10">
                <div class="container">
                    <div class="login-popup">
                        <div class="form-box">
                            <div class="tab tab-nav-simple tab-nav-boxed form-tab">
                                <ul class="nav nav-tabs nav-fill align-items-center border-no justify-content-center mb-5"
                                    role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active border-no lh-1 ls-normal" href="#signin">ورود </a>
                                    </li>
                                    <li class="delimiter">یا</li>
                                    <li class="nav-item">
                                        <a class="nav-link border-no lh-1 ls-normal" href="#register">  ثبت نام</a>
                                    </li>
                                </ul>
                                <div class="tab-content">
                                    <div class="tab-pane active" id="signin">
                                        <form method="POST" action="{{ route('login.post') }}">
                                            @csrf
                                            @if($errors->has('login_error'))
                                                <div class="text-center mb-3" style="color: #333; font-size: 14px;">{{ $errors->first('login_error') }}</div>
                                            @endif
                                            <div class="form-group mb-3">
                                                <input type="text" class="form-control text-left" id="singin-email"
                                                    name="singin-email" placeholder="نام کاربری یا آدرس ایمیل"
                                                    required value="{{ old('singin-email') }}" />
                                            </div>
                                            <div class="form-group">
                                                <input type="password" class="form-control text-left ltr" id="singin-password"
                                                    name="singin-password" placeholder="رمز عبور" required />
                                            </div>
                                            <div class="form-footer">
                                                <div class="form-checkbox">
                                                    <input type="checkbox" class="custom-checkbox" id="signin-remember"
                                                        name="signin-remember" />
                                                    <label class="form-control-label" for="signin-remember">مرا به خاطر بسپار
                                                        </label>
                                                </div>
                                                <a href="#" class="lost-link"> رمز عبور خود را فراموش کرده اید؟</a>
                                            </div>
                                            <button class="btn btn-dark btn-block btn-rounded"
                                                type="submit">ورود</button>
                                        </form>
                                        <div class="form-choice text-center">
                                            <label class="ls-m">یا وارد شوید با</label>
                                            <div class="social-links">
                                                <a href="#"
                                                    class="social-link social-google fab fa-google border-no"></a>
                                                <a href="#"
                                                    class="social-link social-facebook fab fa-facebook-f border-no"></a>
                                                <a href="#"
                                                    class="social-link social-twitter fab fa-twitter border-no"></a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane" id="register">
                                        <form method="POST" action="{{ route('register.post') }}">
                                            @csrf
                                            @if($errors->any() && !$errors->has('login_error'))
                                                <div class="text-center mb-3" style="color: #333; font-size: 14px;">
                                                    @foreach ($errors->all() as $error)
                                                        @if($error !== $errors->first('login_error'))
                                                            <div class="mb-1">{{ $error }}</div>
                                                        @endif
                                                    @endforeach
                                                </div>
                                            @endif
                                            <div class="form-group">
                                                <input type="text" class="form-control text-left" id="register-name"
                                                    name="register-name" placeholder="نام کاربری" required value="{{ old('register-name') }}" />
                                            </div>
                                            <div class="form-group">
                                                <input type="email" class="form-control text-left" id="register-email"
                                                    name="register-email" placeholder="آدرس ایمیل" required value="{{ old('register-email') }}" />
                                            </div>
                                            <div class="form-group">
                                                <input type="tel" class="form-control text-left ltr" id="register-phone"
                                                    name="register-phone" placeholder="شماره تلفن" required value="{{ old('register-phone') }}" />
                                            </div>
                                            <div class="form-group">
                                                <input type="password" class="form-control text-left ltr" id="register-password"
                                                    name="register-password" placeholder="رمز عبور" required />
                                            </div>
                                            <div class="form-footer">
                                                <div class="form-checkbox">
                                                    <input type="checkbox" class="custom-checkbox" id="register-agree"
                                                        name="register-agree" required />
                                                    <label class="form-control-label" for="register-agree">
                                                        اطلاعات شما محفوظ است </label>
                                                </div>
                                            </div>
                                            <button class="btn btn-dark btn-block btn-rounded"
                                                type="submit">ثبت نام</button>
                                        </form>
                                        <div class="form-choice text-center">
                                            <label class="ls-m">یا ثبت نام با</label>
                                            <div class="social-links">
                                                <a href="#"
                                                    class="social-link social-google fab fa-google border-no"></a>
                                                <a href="#"
                                                    class="social-link social-facebook fab fa-facebook-f border-no"></a>
                                                <a href="#"
                                                    class="social-link social-twitter fab fa-twitter border-no"></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
@endsection