@extends('app')
@section('title', 'فروشگاه')
@section('style')
<link rel="stylesheet" type="text/css" href="{{ asset('css/demo4.min.css') }}">
<link rel="stylesheet" type="text/css" href="{{ asset('css/style.min.css') }}">
<style>
.product-media img {
    width: 200px !important;
    height: 225px !important;
    object-fit: cover !important;
}


</style>
@endsection
@section('content')
<main class="main rtl text-right">
			<div class="bannershop page-header">
				<h1 class="page-title">فروشگاه</h1>
				<ul class="breadcrumb">
					<li><a href="demo1.html"><i class="d-icon-home"></i></a></li>
					<li class="delimiter">/</li>
					<li>فروشگاه</li>
				</ul>
			</div>
			<!-- End PageHeader -->
			<div class="page-content mb-10 pb-3">
				<div class="container ltr">
				

					<div class="row main-content-wrap gutter-lg">
						
<div class="page-content mb-10 pb-6">
				<div class="container">
					<div class="toolbox-wrap">
						<aside class="sidebar sidebar-fixed shop-sidebar closed">
							<div class="sidebar-overlay"></div>
							<a class="sidebar-close" href="#"><i class="d-icon-times"></i></a>
							<div class="sidebar-content">
								<div class="mb-0 mb-lg-4">
									<div class="filter-actions">
										<a href="#" class="filter-clean">پاک کردن همه</a>
									</div>
									<div class="row cols-lg-4">
										<div class="widget">
											<h3 class="widget-title">دسته‌بندی ها</h3>
											<ul class="widget-body filter-items">
												@foreach($categories as $category)
													<li><a href="{{ route('shop', ['category' => $category->id]) }}">{{ $category->name }}</a></li>
													@if($category->children->count() > 0)
														@foreach($category->children as $child)
															<li class="ml-4"><a href="{{ route('shop', ['category' => $child->id]) }}">{{ $child->name }}</a></li>
														@endforeach
													@endif
												@endforeach
											</ul>
										</div>
										<div class="widget">
											<h3 class="widget-title">رنگ</h3>
											<ul class="widget-body filter-items">
												<li><a href="#">سیاه</a></li>
												<li><a href="#">آبی</a></li>
												<li><a href="#">قهو ه ای</a></li>
												<li><a href="#">سبز</a></li>
											</ul>
										</div>
										<div class="widget price-with-count">
											<h3 class="widget-title">قیمت</h3>
											<ul class="widget-body filter-items filter-price">
												<li class="active"><a href="#">همه </a></li>
												<li><a href="#"> 1،800،000 تومان  -  2,800,000 تومان </a></li>
												<li><a href="#"> 2,800,000 تومان  -  3،800،000 تومان  </a></li>
												<li><a href="#"> بالای 3,800,000</a></li>
											</ul>
										</div>
										<div class="widget">
											<h3 class="widget-title">برچسب ها</h3>
											<div class="widget-body pt-2">
												<a href="#" class="tag">کیف</a>
												<a href="#" class="tag">کلاسیک</a>
												<a href="#" class="tag">مکالمه</a>
												<a href="#" class="tag"> تناسب</a>
												<a href="#" class="tag">سبز</a>
												<a href="#" class="tag">جک و جونز</a>
												<a href="#" class="tag">شلوار جین</a>
												<a href="#" class="tag"> جامپر</a>
												<a href="#" class="tag">چرم</a>
												<a href="#" class="tag">دیزل</a>
												<a href="#" class="tag">مردانه</a>
											</div>
										</div>
									</div>
								</div>
							</div>
						</aside>
						<div class="toolbox sticky-toolbox sticky-content fix-top rtl">
							<div class="toolbox-right">
								<a href="#" class="toolbox-item left-sidebar-toggle btn btn-outline btn-primary btn-rounded btn-icon-left font-primary"><i class="d-icon-filter-2"></i>فیلتر</a>
							</div>
							<div class="toolbox-left">
								<div class="toolbox-item toolbox-sort select-box text-dark">
									<label>مرتب شود با  :</label>
									<select name="orderby" class="form-control">
										<option value="default">پیش فرض</option>
										<option value="popularity" selected="selected">محبوبترین</option>
										<option value="rating">میانگین امتیاز</option>
										 <option value="date">ترندها</option>
										<option value="price-low">کمترین قیمت</option>
										<option value="price-high">بیشترین قیمت</option>
										<option value="">پاک کردن</option>
									</select>
								</div>
								<div class="toolbox-item toolbox-layout ">
									<a href="shop.html" class="d-icon-mode-grid btn-layout active"></a>
								</div>
							</div>
						</div>
					</div>
					<div class="row cols-2 cols-sm-4 product-wrapper">
								@foreach($products as $product)
								<div class="product-wrap">
									<div class="product">
										<figure class="product-media">
											<a href="{{ route('product.show', $product->id) }}">
												@if($product->image)
													<img src="{{ asset($product->image) }}" 
														 alt="{{ $product->name }}" 
														 width="200" height="225"
														 loading="lazy">
												@else
													<img src="{{ asset('images/product/placeholder.png') }}" 
														 alt="{{ $product->name }}" 
														 width="200" height="225" 
														 loading="lazy">
												@endif
											</a>
											@if($product->no_offer_price && $product->no_offer_price > $product->price)
											<div class="product-label-group">
												<label class="product-label label-sale">%{{ intval(($product->no_offer_price - $product->price) / $product->no_offer_price * 100) }}</label>
											</div>
											@endif
											<div class="product-action-vertical">
												<a href="{{ route('product.show', $product->id) }}" class="btn-product-icon btn-view" 
													title="مشاهده محصول">
													<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
														<path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
													</svg>
												</a>
												<a href="#" class="btn-product-icon btn-wishlist"
													onclick="event.preventDefault(); document.getElementById('wishlist-form-{{ $product->id }}').submit();"
													title="اضافه به علاقه مندی">
													<i class="d-icon-heart"></i>
												</a>
											</div>
										</figure>
										<div class="product-details rtl">
											<div class="product-cat">
												<a href="#">{{ $product->category->name ?? 'دسته‌بندی نامشخص' }}</a>
											</div>
											<h3 class="product-name">
												<a href="{{ route('product.show', $product->id) }}">{{ $product->name }}</a>
											</h3>
											<div class="product-price">
												@if($product->no_offer_price && $product->no_offer_price > $product->price)
													<ins class="new-price">{{ number_format($product->price) }} تومان</ins>
													<del class="old-price">{{ number_format($product->no_offer_price) }} تومان</del>
												@else
													<span class="price">{{ number_format($product->price) }} تومان</span>
												@endif
											</div>
											<div class="ratings-container">
												<div class="ratings-full">
													<span class="ratings" style="width:60%"></span>
													<span class="tooltiptext tooltip-top"></span>
												</div>
											</div>
										</div>
									</div>
								</div>
								<form id="wishlist-form-{{ $product->id }}" action="{{ route('wishlist.store') }}" method="POST" style="display:none;">
								    @csrf
								    <input type="hidden" name="product_id" value="{{ $product->id }}">
								</form>
								@endforeach
							</div>
					























					<nav class="toolbox toolbox-pagination">
						<p class="show-info mr-sm-auto">
							درحال نمایش {{ $products->count() }} از {{ $products->total() }} محصول
						</p>
						{{ $products->links() }}
					</nav>
				</div>
			</div>
						
					</div>
				</div>
			</div>
		</main>
@endsection