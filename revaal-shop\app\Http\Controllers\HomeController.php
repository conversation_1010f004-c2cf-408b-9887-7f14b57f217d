<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Blog;
use App\Models\BlogCategory;
use App\Models\Product;
use App\Models\ProductCategory;

class HomeController extends Controller
{
    /**
     * نمایش صفحه اصلی
     */
    public function index()
    {
        // آخرین مقالات بلاگ برای نمایش در صفحه اصلی
        $latestBlogs = Blog::published()
            ->with(['category', 'author'])
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        return view('home', compact('latestBlogs'));
    }

    /**
     * نمایش صفحه فروشگاه
     */
    public function shop(Request $request)
    {
        $query = Product::with('category')
            ->where('status', 1);

        // فیلتر بر اساس دسته‌بندی
        if ($request->has('category')) {
            $query->where('product_category', $request->category);
        }

        // فیلتر بر اساس قیمت
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // مرتب سازی
        $orderBy = $request->get('orderby', 'created_at');
        $order = $request->get('order', 'desc');

        switch ($orderBy) {
            case 'price-low':
                $query->orderBy('price', 'asc');
                break;
            case 'price-high':
                $query->orderBy('price', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'popularity':
                $query->orderBy('is_special', 'desc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        $products = $query->paginate(12);

        $categories = ProductCategory::whereNull('parent')
            ->with('children')
            ->get();

        return view('product.index', compact('products', 'categories'));
    }

    /**
     * نمایش جزئیات محصول
     */
    public function product($id = null)
    {
        if ($id) {
            $product = Product::with('category', 'options', 'detail')->findOrFail($id);
            
            // نمایش محصولات مشابه
            $relatedProducts = Product::with('category')
                ->where('status', 1)
                ->where('product_category', $product->product_category)
                ->where('id', '!=', $product->id)
                ->orderBy('created_at', 'desc')
                ->take(4)
                ->get();
            
            // قبلی و بعدی از کل محصولات فعال
            $prevProduct = Product::where('status', 1)
                ->where('id', '<', $product->id)
                ->orderBy('id', 'desc')
                ->first();
            $nextProduct = Product::where('status', 1)
                ->where('id', '>', $product->id)
                ->orderBy('id', 'asc')
                ->first();
            
            return view('product.show', compact('product', 'relatedProducts', 'prevProduct', 'nextProduct'));
        }
        
        // اگر ID نداشته باشد به صفحه محصولات برگردان
        return redirect()->route('shop');
    }



    /**
     * نمایش صفحه درباره ما
     */
    public function about()
    {
        return view('about');
    }

    /**
     * نمایش صفحه تماس با ما
     */
    public function contact()
    {
        return view('contact');
    }

    /**
     * نمایش صفحه سبد خرید
     */
    public function cart()
    {
        return view('cart');
    }

    /**
     * نمایش صفحه علاقه‌مندی‌ها
     */
    public function wishlist()
    {
        return view('wishlist');
    }

    /**
     * صفحه ورود کاربران
     */
    public function login()
    {
        return view('login');
    }

    /**
     * صفحه ثبت نام کاربران
     */
    public function register()
    {
        return view('register');
    }
}
