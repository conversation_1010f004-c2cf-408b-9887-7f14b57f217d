<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Wishlist;
use Illuminate\Support\Facades\Auth;

class WishlistController extends Controller
{
    // نمایش لیست علاقه‌مندی‌های کاربر
    public function index()
    {
        if (Auth::check()) {
            $wishlists = Wishlist::where('user_id', Auth::id())->with('product')->get();
            if ($wishlists->isEmpty()) {
                return view('wishlist.wishlist-empty');
            }
            return view('wishlist.wishlist', compact('wishlists'));
        } else {
            $wishlistIds = session('wishlist', []);
            $wishlists = collect();
            if (!empty($wishlistIds)) {
                $products = \App\Models\Product::whereIn('id', $wishlistIds)->get();
                foreach ($products as $product) {
                    $wishlists->push((object)[
                        'id' => $product->id,
                        'product' => $product
                    ]);
                }
            }
            if ($wishlists->isEmpty()) {
                return view('wishlist.wishlist-empty');
            }
            return view('wishlist.wishlist', compact('wishlists'));
        }
    }

    // افزودن محصول به علاقه‌مندی
    public function store(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);
        if (Auth::check()) {
            $wishlist = Wishlist::firstOrCreate([
                'user_id' => Auth::id(),
                'product_id' => $request->product_id,
            ]);
            return back()->with('success', 'محصول به علاقه‌مندی‌ها اضافه شد.');
        } else {
            $wishlist = session('wishlist', []);
            if (!in_array($request->product_id, $wishlist)) {
                $wishlist[] = $request->product_id;
                session(['wishlist' => $wishlist]);
            }
            return back()->with('success', 'محصول به علاقه‌مندی‌ها اضافه شد.');
        }
    }

    // حذف محصول از علاقه‌مندی
    public function destroy($id)
    {
        if (Auth::check()) {
            $wishlist = Wishlist::where('user_id', Auth::id())->where('id', $id)->first();
            if ($wishlist) {
                $wishlist->delete();
            }
            return back()->with('success', 'محصول از علاقه‌مندی‌ها حذف شد.');
        } else {
            $wishlist = session('wishlist', []);
            $wishlist = array_diff($wishlist, [$id]);
            session(['wishlist' => $wishlist]);
            return back()->with('success', 'محصول از علاقه‌مندی‌ها حذف شد.');
        }
    }
}
