@extends('app')
@section('title', 'لیست علاقه مندی ها')
@section('style')
<link rel="stylesheet" type="text/css" href="{{ asset('css/demo4.min.css') }}">
<link rel="stylesheet" type="text/css" href="{{ asset('css/style.min.css') }}">
@endsection
@section('content')
<main class="main rtl text-right">
            <nav class="breadcrumb-nav">
                <div class="container">
                    <ul class="breadcrumb">
                        <li><a href="demo1.html"><i class="d-icon-home"></i></a></li>
                        <li>علاقه مندی</li>
                    </ul>
                </div>
            </nav>
            <div class="page-content pt-10 pb-10 mb-2">
                <div class="container">
                    <table class="shop-table wishlist-table mt-2 mb-4">
                        <thead>
                            <tr>
                                <th class="product-name"><span>محصول</span></th>
                                <th></th>
                                <th class="product-price"><span>قیمت</span></th>
                                <th class="product-stock-status"><span>موجود در انبار</span></th>
                                <th class="product-add-to-cart"></th>
                                <th class="product-remove"></th>
                            </tr>
                        </thead>
                        <tbody class="wishlist-items-wrapper">
                        @forelse($wishlists as $item)
                            <tr>
                                <td class="product-thumbnail">
                                    <a href="{{ route('product.show', $item->product->id) }}">
                                        <figure>
                                            <img src="{{ asset($item->product->image ?? 'images/products/default.jpg') }}" width="100" height="100" alt="product">
                                        </figure>
                                    </a>
                                </td>
                                <td class="product-name">
                                    <a href="{{ route('product.show', $item->product->id) }}">{{ $item->product->name ?? '-' }}</a>
                                </td>
                                <td class="product-price">
                                    <span class="amount"> {{ number_format($item->product->price ?? 0) }} تومان </span>
                                </td>
                                <td class="product-stock-status">
                                    @if(($item->product->stock ?? 0) > 0)
                                        <span class="wishlist-in-stock">{{ $item->product->stock }} عدد در انبار</span>
                                    @else
                                        <span class="wishlist-out-stock">موجود نیست</span>
                                    @endif
                                </td>
                                <td class="product-add-to-cart">
                                    <a href="{{ route('product.show', $item->product->id) }}" class="btn-product btn-primary"><span>مشاهده و انتخاب</span></a>
                                </td>
                                <td class="product-remove">
                                    <form action="{{ route('wishlist.destroy', $item->id) }}" method="POST">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="remove" title="حذف محصول"><i class="fas fa-times"></i></button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr><td colspan="6">لیست علاقه‌مندی شما خالی است.</td></tr>
                        @endforelse
                        </tbody>
                    </table>
                    <div class="social-links share-on">
                        <h5 class=" font-weight-bold mb-0 mr-4 ls-s">صفحات اجتماعی ما : </h5>
                        <a href="#" class="social-link social-icon social-facebook" title="Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link social-icon social-twitter" title="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link social-icon social-pinterest" title="Pinterest">
                            <i class="fab fa-pinterest-p"></i>
                        </a>
                        <a href="#" class="social-link social-icon social-email" title="Email">
                            <i class="far fa-envelope"></i>
                        </a>
                        <a href="#" class="social-link social-icon social-whatsapp" title="Whatsapp">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                    </div>
                </div>
            </div>
        </main>
@endsection