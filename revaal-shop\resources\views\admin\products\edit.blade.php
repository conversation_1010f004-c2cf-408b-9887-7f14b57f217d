@extends('admin.app')
@section('title', 'ویرایش محصول')

@section('content')
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">ویرایش محصول</h4>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif
                
                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif
                
                @if($errors->any())
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif
                
                <form action="{{ route('admin.products.update', $product->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">نام محصول</label>
                                <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror"
                                    value="{{ old('name', $product->name) }}" placeholder="نام محصول">
                                @error('name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="mb-3">
                                <label for="product_category" class="form-label">دسته‌بندی</label>
                                <select name="product_category" id="product_category" class="form-control @error('product_category') is-invalid @enderror" required>
                                    <option value="">انتخاب دسته‌بندی</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('product_category', $product->product_category) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('product_category')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-4">
                            <div class="mb-3">
                                <label for="price" class="form-label">قیمت (تومان)</label>
                                <input type="number" name="price" id="price" class="form-control @error('price') is-invalid @enderror"
                                    value="{{ old('price', $product->price) }}" placeholder="قیمت محصول">
                                @error('price') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="mb-3">
                                <label for="no_offer_price" class="form-label">قیمت بدون تخفیف (تومان)</label>
                                <input type="number" name="no_offer_price" id="no_offer_price"
                                    class="form-control @error('no_offer_price') is-invalid @enderror"
                                    value="{{ old('no_offer_price', $product->no_offer_price) }}" placeholder="قیمت بدون تخفیف">
                                @error('no_offer_price') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="mb-3">
                                <label for="inventory" class="form-label">موجودی</label>
                                <input type="number" name="inventory" id="inventory"
                                    class="form-control @error('inventory') is-invalid @enderror"
                                    value="{{ old('inventory', $product->inventory) }}" placeholder="تعداد موجودی">
                                @error('inventory') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">توضیحات</label>
                                <textarea name="description" id="description"
                                    class="form-control @error('description') is-invalid @enderror"
                                    rows="7" placeholder="توضیحات محصول">{{ old('description', $product->description) }}</textarea>
                                @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mb-3">
                                <label for="image" class="form-label">تصویر اصلی محصول</label>
                                <input type="file" name="image" id="image" class="form-control @error('image') is-invalid @enderror" accept="image/jpeg,image/png,image/jpg,image/gif" onchange="previewImage(this, 'imagePreview')">
                                @error('image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="mt-2">
                                    @if($product->image)
                                        <div class="position-relative d-inline-block">
                                            <img src="{{ asset($product->image) }}" alt="تصویر فعلی" class="img-thumbnail" style="max-width: 200px;">
                                        </div>
                                    @endif
                                    <img id="imagePreview" src="#" alt="پیش‌نمایش تصویر جدید" style="max-width: 200px; display: none;">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mb-3">
                                <label for="other_image" class="form-label">تصاویر دیگر</label>
                                <input type="file" name="other_image[]" id="other_image" class="form-control @error('other_image') is-invalid @enderror" accept="image/jpeg,image/png,image/jpg,image/gif" multiple onchange="previewMultipleImages(this, 'otherImagesPreview')">
                                @error('other_image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="mt-2">
                                    @if($product->other_image)
                                        <div class="d-flex flex-wrap gap-2 mb-2">
                                            @foreach(json_decode($product->other_image) as $image)
                                                <div class="position-relative">
                                                    <img src="{{ asset($image) }}" alt="تصویر دیگر" class="img-thumbnail" style="max-width: 150px;">
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif
                                    <div id="otherImagesPreview" class="d-flex flex-wrap gap-2"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">نوع محصول</label>
                                <input type="text" name="type" id="type" class="form-control @error('type') is-invalid @enderror"
                                    value="{{ old('type', $product->type) }}" placeholder="نوع محصول را وارد کنید">
                                @error('type') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input type="checkbox" class="form-check-input" id="is_special" name="is_special" value="1" {{ old('is_special', $product->is_special) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_special">محصول ویژه</label>
                                </div>
                                @error('is_special')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                <div class="col-lg-12">
               <div class="mb-3">
                     <div class="form-check form-switch">
                       <input type="checkbox" class="form-check-input" id="status" name="status" value="1"
                    {{ old('status', $product->status) ? 'checked' : '' }}>
                <label class="form-check-label" for="status">فعال</label>
                   </div>
                     @error('status')
                      <div class="invalid-feedback">{{ $message }}</div>
                   @enderror
                 </div>
            </div>
             </div>


                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mb-3">
                                <label for="meta_description" class="form-label">توضیحات متا</label>
                                <textarea name="meta_description" id="meta_description"
                                    class="form-control @error('meta_description') is-invalid @enderror"
                                    rows="3">{{ old('meta_description', $product->meta_description) }}</textarea>
                                <small class="text-muted">حداکثر 160 کاراکتر</small>
                                @error('meta_description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mb-3">
                                <label for="meta_keywords" class="form-label">کلمات کلیدی</label>
                                <input type="text" name="meta_keywords" id="meta_keywords"
                                    class="form-control @error('meta_keywords') is-invalid @enderror"
                                    value="{{ old('meta_keywords', $product->meta_keywords) }}"
                                    placeholder="کلمات کلیدی را با کاما جدا کنید">
                                <small class="text-muted">کلمات کلیدی را با کاما (,) از هم جدا کنید</small>
                                @error('meta_keywords') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                        </div>
                    </div>

                    <!-- بخش گزینه‌های محصول -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title">گزینه‌های محصول (سایز، رنگ و ...)</h5>
                        </div>
                        <div class="card-body">
                            <div id="product-options">
                                @php
                                    $oldOptions = old('options');
                                    $options = $oldOptions ?? ($product->options ? $product->options->toArray() : []);
                                @endphp
                                @if(count($options) > 0)
                                    @foreach($options as $index => $option)
                                        <div class="option-item border p-3 mb-3 rounded">
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">نام گزینه</label>
                                                        <input type="text" name="options[{{ $index }}][name]" class="form-control" value="{{ $option['name'] ?? '' }}" placeholder="مثال: تی شرت آبی سایز L">
                                                    </div>
                                                </div>
                                                <div class="col-lg-3">
                                                    <div class="mb-3">
                                                        <label class="form-label">رنگ</label>
                                                        <input type="text" name="options[{{ $index }}][color_name]" class="form-control" value="{{ $option['color_name'] ?? '' }}" placeholder="آبی">
                                                    </div>
                                                </div>
                                                <div class="col-lg-3">
                                                    <div class="mb-3">
                                                        <label class="form-label">کد رنگ</label>
                                                        <input type="color" name="options[{{ $index }}][color_code]" class="form-control" value="{{ $option['color_code'] ?? '#000000' }}">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-2">
                                                    <div class="mb-3">
                                                        <label class="form-label">سایز</label>
                                                        <select name="options[{{ $index }}][size]" class="form-control">
                                                            <option value="">انتخاب سایز</option>
                                                            <option value="s" @if(($option['size'] ?? '') == 's') selected @endif>S</option>
                                                            <option value="m" @if(($option['size'] ?? '') == 'm') selected @endif>M</option>
                                                            <option value="l" @if(($option['size'] ?? '') == 'l') selected @endif>L</option>
                                                            <option value="xl" @if(($option['size'] ?? '') == 'xl') selected @endif>XL</option>
                                                            <option value="2xl" @if(($option['size'] ?? '') == '2xl') selected @endif>2XL</option>
                                                            <option value="3xl" @if(($option['size'] ?? '') == '3xl') selected @endif>3XL</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-lg-3">
                                                    <div class="mb-3">
                                                        <label class="form-label">قیمت (تومان)</label>
                                                        <input type="number" name="options[{{ $index }}][price]" class="form-control" value="{{ $option['price'] ?? '' }}" placeholder="0">
                                                    </div>
                                                </div>
                                                <div class="col-lg-2">
                                                    <div class="mb-3">
                                                        <label class="form-label">موجودی</label>
                                                        <input type="number" name="options[{{ $index }}][inventory]" class="form-control" value="{{ $option['inventory'] ?? '' }}" placeholder="0">
                                                    </div>
                                                </div>
                                                <div class="col-lg-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">یادداشت</label>
                                                        <input type="text" name="options[{{ $index }}][note]" class="form-control" value="{{ $option['note'] ?? '' }}" placeholder="توضیحات اضافی">
                                                    </div>
                                                </div>
                                                <div class="col-lg-1">
                                                    <div class="mb-3">
                                                        <label class="form-label">&nbsp;</label>
                                                        <button type="button" class="btn btn-danger btn-sm d-block remove-option">حذف</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="option-item border p-3 mb-3 rounded">
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="mb-3">
                                                    <label class="form-label">نام گزینه</label>
                                                    <input type="text" name="options[0][name]" class="form-control" placeholder="مثال: تی شرت آبی سایز L">
                                                </div>
                                            </div>
                                            <div class="col-lg-3">
                                                <div class="mb-3">
                                                    <label class="form-label">رنگ</label>
                                                    <input type="text" name="options[0][color_name]" class="form-control" placeholder="آبی">
                                                </div>
                                            </div>
                                            <div class="col-lg-3">
                                                <div class="mb-3">
                                                    <label class="form-label">کد رنگ</label>
                                                    <input type="color" name="options[0][color_code]" class="form-control">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-2">
                                                <div class="mb-3">
                                                    <label class="form-label">سایز</label>
                                                    <select name="options[0][size]" class="form-control">
                                                        <option value="">انتخاب سایز</option>
                                                        <option value="s">S</option>
                                                        <option value="m">M</option>
                                                        <option value="l">L</option>
                                                        <option value="xl">XL</option>
                                                        <option value="2xl">2XL</option>
                                                        <option value="3xl">3XL</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-3">
                                                <div class="mb-3">
                                                    <label class="form-label">قیمت (تومان)</label>
                                                    <input type="number" name="options[0][price]" class="form-control" placeholder="0">
                                                </div>
                                            </div>
                                            <div class="col-lg-2">
                                                <div class="mb-3">
                                                    <label class="form-label">موجودی</label>
                                                    <input type="number" name="options[0][inventory]" class="form-control" placeholder="0">
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="mb-3">
                                                    <label class="form-label">یادداشت</label>
                                                    <input type="text" name="options[0][note]" class="form-control" placeholder="توضیحات اضافی">
                                                </div>
                                            </div>
                                            <div class="col-lg-1">
                                                <div class="mb-3">
                                                    <label class="form-label">&nbsp;</label>
                                                    <button type="button" class="btn btn-danger btn-sm d-block remove-option">حذف</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <button type="button" class="btn btn-success btn-sm" id="add-option">+ افزودن گزینه جدید</button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="mb-3">
                                <label for="dimension" class="form-label">ابعاد</label>
                                <input type="text" name="dimension" id="dimension" class="form-control" value="{{ old('dimension', optional($product->detail)->dimension) }}">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="mb-3">
                                <label for="country" class="form-label">کشور سازنده</label>
                                <input type="text" name="country" id="country" class="form-control" value="{{ old('country', optional($product->detail)->country) }}">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="mb-3">
                                <label for="suitable_for" class="form-label">مناسب برای</label>
                                <input type="text" name="suitable_for" id="suitable_for" class="form-control" value="{{ old('suitable_for', optional($product->detail)->suitable_for) }}">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="mb-3">
                                <label for="manufacturer" class="form-label">تولید کننده</label>
                                <input type="text" name="manufacturer" id="manufacturer" class="form-control" value="{{ old('manufacturer', optional($product->detail)->manufacturer) }}">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mb-3">
                                <label for="introduction" class="form-label">معرفی محصول</label>
                                <textarea name="introduction" id="introduction" class="form-control" rows="4">{{ old('introduction', optional($product->detail)->introduction) }}</textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="mb-3">
                                <label for="introduction_video" class="form-label">ویدیو معرفی (لینک یا آپلود)</label>
                                <input type="text" name="introduction_video" id="introduction_video" class="form-control" value="{{ old('introduction_video', optional($product->detail)->introduction_video) }}">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="mb-3">
                                <label for="pexpline" class="form-label">توضیح کوتاه معرفی</label>
                                <textarea name="pexpline" id="pexpline" class="form-control" rows="2">{{ old('pexpline', optional($product->detail)->pexpline) }}</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12 d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.products.index') }}" class="btn btn-light">بازگشت</a>
                            <button type="submit" class="btn btn-primary">ذخیره تغییرات</button>
                        </div>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        }
        reader.readAsDataURL(input.files[0]);
    }
}

function previewMultipleImages(input, previewId) {
    const preview = document.getElementById(previewId);
    preview.innerHTML = '';
    
    if (input.files) {
        Array.from(input.files).forEach(file => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.style.maxWidth = '150px';
                img.style.margin = '5px';
                preview.appendChild(img);
            }
            reader.readAsDataURL(file);
        });
    }
}

// مدیریت گزینه‌های محصول
let optionIndex = {{ $product->options ? $product->options->count() : 0 }};

document.getElementById('add-option').addEventListener('click', function() {
    const optionsContainer = document.getElementById('product-options');
    const newOption = document.createElement('div');
    newOption.className = 'option-item border p-3 mb-3 rounded';
    newOption.innerHTML = `
        <div class="row">
            <div class="col-lg-6">
                <div class="mb-3">
                    <label class="form-label">نام گزینه</label>
                    <input type="text" name="options[${optionIndex}][name]" class="form-control" placeholder="مثال: تی شرت آبی سایز L">
                </div>
            </div>
            <div class="col-lg-3">
                <div class="mb-3">
                    <label class="form-label">رنگ</label>
                    <input type="text" name="options[${optionIndex}][color_name]" class="form-control" placeholder="آبی">
                </div>
            </div>
            <div class="col-lg-3">
                <div class="mb-3">
                    <label class="form-label">کد رنگ</label>
                    <input type="color" name="options[${optionIndex}][color_code]" class="form-control" value="#000000">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-2">
                <div class="mb-3">
                    <label class="form-label">سایز</label>
                    <select name="options[${optionIndex}][size]" class="form-control">
                        <option value="">انتخاب سایز</option>
                        <option value="s">S</option>
                        <option value="m">M</option>
                        <option value="l">L</option>
                        <option value="xl">XL</option>
                        <option value="2xl">2XL</option>
                        <option value="3xl">3XL</option>
                    </select>
                </div>
            </div>
            <div class="col-lg-3">
                <div class="mb-3">
                    <label class="form-label">قیمت (تومان)</label>
                    <input type="number" name="options[${optionIndex}][price]" class="form-control" placeholder="0">
                </div>
            </div>
            <div class="col-lg-2">
                <div class="mb-3">
                    <label class="form-label">موجودی</label>
                    <input type="number" name="options[${optionIndex}][inventory]" class="form-control" placeholder="0">
                </div>
            </div>
            <div class="col-lg-4">
                <div class="mb-3">
                    <label class="form-label">یادداشت</label>
                    <input type="text" name="options[${optionIndex}][note]" class="form-control" placeholder="توضیحات اضافی">
                </div>
            </div>
            <div class="col-lg-1">
                <div class="mb-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="button" class="btn btn-danger btn-sm d-block remove-option">حذف</button>
                </div>
            </div>
        </div>
    `;
    
    optionsContainer.appendChild(newOption);
    optionIndex++;
});

// حذف گزینه
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-option')) {
        const optionItem = e.target.closest('.option-item');
        if (document.querySelectorAll('.option-item').length > 1) {
            optionItem.remove();
        } else {
            alert('حداقل یک گزینه باید موجود باشد');
        }
    }
});
</script>
@endsection
