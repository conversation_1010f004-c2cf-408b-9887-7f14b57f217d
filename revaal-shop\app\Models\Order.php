<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'customer_name',
        'phone',
        'address',
        'postal_code',
        'province',
        'city',
        'delivery_type',
        'shipping_method',
        'shipping_cost',
        'cost',
        'status',
        'tracking_code',
        'description',
        'notes'
    ];

    // رابطه با کاربر
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderMeta::class);
    }

    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'pending' => 'در انتظار بررسی',
            'in_progress' => 'در حال پردازش',
            'completed' => 'تکمیل شده',
            'cancelled' => 'لغو شده',
            'payback' => 'مرجوع شده',
            default => 'نامشخص'
        };
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'in_progress' => 'info',
            'completed' => 'success',
            'cancelled' => 'danger',
            'payback' => 'secondary',
            default => 'secondary'
        };
    }
}
 