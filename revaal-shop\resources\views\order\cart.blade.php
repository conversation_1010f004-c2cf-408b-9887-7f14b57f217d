@extends('app')
@section('title', 'سبد خرید')
@section('style')
<link rel="stylesheet" type="text/css" href="{{ asset('css/demo4.min.css') }}">
<link rel="stylesheet" type="text/css" href="{{ asset('css/style.min.css') }}">
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection
@section('content')
<main class="main cart rtl text-right">
    <div class="page-content pt-7 pb-10">
        <div class="step-by pr-4 pl-4">
            <h3 class="title title-simple title-step active"><a href="{{ route('cart.index') }}">1. سبد خرید</a></h3>
            <h3 class="title title-simple title-step"><a href="#">2. صورت حساب</a></h3>
            <h3 class="title title-simple title-step"><a href="#">3. تکمیل خرید</a></h3>
        </div>
        <div class="cartmain container mt-7 mb-2">
            <div class="row">
                <div class="col-lg-8 col-md-12 pr-lg-4">
                    @if($cartItems && $cartItems->count() > 0)
                    <table class="shop-table cart-table">
                        <thead>
                            <tr>
                                <th><span>محصول</span></th>
                                <th></th>
                                <th><span>قیمت</span></th>
                                <th><span>تعداد</span></th>
                                <th>قیمت کل</th>
                                <th>حذف</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cartItems as $item)
                            <tr data-cart-id="{{ $item->id }}">
                                <td class="product-thumbnail">
                                    <figure>
                                        <a href="{{ route('product.show', $item->product->id) }}">
                                            <img src="{{ asset($item->product->image ?? 'images/products/default.jpg') }}" 
                                                 width="100" height="100" alt="{{ $item->product->name }}">
                                        </a>
                                    </figure>
                                </td>
                                <td class="product-name">
                                    <div class="product-name-section">
                                        <a href="{{ route('product.show', $item->product->id) }}">{{ $item->product->name }}</a>
                                    </div>
                                </td>
                                <td class="product-subtotal">
                                    <span class="amount">{{ number_format($item->product->price) }} تومان</span>
                                </td>
                                <td class="product-quantity">
                                    <div class="input-group">
                                        <button class="quantity-minus d-icon-minus" onclick="updateQuantity({{ $item->id }}, -1)"></button>
                                        <input class="quantity form-control" type="number" min="1" max="1000000" 
                                               value="{{ $item->count }}" data-cart-id="{{ $item->id }}">
                                        <button class="quantity-plus d-icon-plus" onclick="updateQuantity({{ $item->id }}, 1)"></button>
                                    </div>
                                </td>
                                <td class="product-price">
                                    <span class="amount item-total">{{ number_format($item->product->price * $item->count) }} تومان</span>
                                </td>
                                <td class="product-close">
                                    <a href="#" class="product-remove" title="حذف محصول" onclick="removeFromCart({{ $item->id }})">
                                        <i class="fas fa-times"></i>
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    @else
                    <div class="alert alert-info text-center">
                        <h4>سبد خرید شما خالی است</h4>
                        <p>برای افزودن محصول به سبد خرید، به فروشگاه بروید</p>
                        <a href="{{ route('shop') }}" class="btn btn-primary">مشاهده محصولات</a>
                    </div>
                    @endif
                    
                    <div class="cart-actions mb-6 pt-4">
                        <a href="{{ route('shop') }}" class="btn btn-dark btn-md btn-rounded btn-icon-left ml-4 mb-4">
                            ادامه خرید<i class="d-icon-arrow-left"></i>
                        </a>
                        <button type="button" id="update-cart" class="btn btn-outline btn-dark btn-md btn-rounded">
                            به روز رسانی سبد خرید
                        </button>
                    </div>
                    
                    <div class="cart-coupon-box mb-8">
                        <h4 class="title coupon-title text-uppercase ls-m">کد تخفیف</h4>
                        <input type="text" name="coupon_code" class="input-text form-control text-grey ls-m mb-4" 
                               id="coupon_code" value="" placeholder="سریال تخفیف را وارد کنید">
                        <button type="button" class="btn btn-md btn-dark btn-rounded btn-outline">
                            اعمال تخفیف
                        </button>
                    </div>
                </div>
                
                <aside class="col-lg-4 sticky-sidebar-wrapper">
                    <div class="sticky-sidebar" data-sticky-options="{'bottom': 20}">
                        <form id="checkout-form">
                            @csrf
                            <div class="summary mb-4">
                                <h3 class="summary-title">جمع سبد خرید</h3>
                                <table class="shipping">
                                    <tr class="summary-subtotal">
                                        <td>
                                            <h4 class="summary-subtitle">جمع محصولات</h4>
                                        </td>
                                        <td>
                                            <p class="summary-subtotal-price">{{ number_format($subtotal ?? 0) }} تومان</p>
                                        </td>
                                    </tr>
                                    <tr class="sumnary-shipping shipping-row-last">
                                        <td colspan="2">
                                            <h4 class="summary-subtitle">روش ارسال (فقط داخل ایران)</h4>
                                            <ul>
                                                <li>
                                                    <div class="custom-radio">
                                                        <input type="radio" id="in_person" name="shipping_method" 
                                                               value="in_person" class="custom-control-input" checked>
                                                        <label class="custom-control-label" for="in_person">
                                                            دریافت حضوری (رایگان)
                                                        </label>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="custom-radio">
                                                        <input type="radio" id="tipax" name="shipping_method" 
                                                               value="tipax" class="custom-control-input">
                                                        <label class="custom-control-label" for="tipax">
                                                            تیپاکس (50,000 تومان)
                                                        </label>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="custom-radio">
                                                        <input type="radio" id="post_express" name="shipping_method" 
                                                               value="post_express" class="custom-control-input">
                                                        <label class="custom-control-label" for="post_express">
                                                            پست پیشتاز (30,000 تومان)
                                                        </label>
                                                    </div>
                                                </li>
                                            </ul>
                                        </td>
                                    </tr>
                                </table>
                                
                                <div class="shipping-address">
                                    <label>ارسال خرید به:</label>
                                    
                                    <div class="form-group mb-3">
                                        <input type="text" class="form-control" name="customer_name" 
                                               placeholder="نام و نام خانوادگی" required>
                                    </div>
                                    
                                    <div class="form-group mb-3">
                                        <input type="text" class="form-control" name="phone" 
                                               placeholder="شماره تماس" required>
                                    </div>
                                    
                                    <div class="select-box mb-3">
                                        <select name="province" id="province" class="form-control" required>
                                            <option value="">انتخاب استان</option>
                                        </select>
                                    </div>
                                    
                                    <div class="select-box mb-3">
                                        <select name="city" id="city" class="form-control" required>
                                            <option value="">انتخاب شهر</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group mb-3">
                                        <textarea class="form-control" name="address" rows="3" 
                                                  placeholder="آدرس کامل" required></textarea>
                                    </div>
                                    
                                    <div class="form-group mb-3">
                                        <input type="text" class="form-control" name="postal_code" 
                                               placeholder="کد پستی" required>
                                    </div>
                                </div>
                                
                                <table class="total">
                                    <tr class="summary-shipping">
                                        <td>
                                            <h4 class="summary-subtitle">هزینه ارسال</h4>
                                        </td>
                                        <td>
                                            <p class="summary-shipping-price">0 تومان</p>
                                        </td>
                                    </tr>
                                    <tr class="summary-subtotal">
                                        <td>
                                            <h4 class="summary-subtitle">جمع کل</h4>
                                        </td>
                                        <td>
                                            <p class="summary-total-price ls-s">{{ number_format($subtotal ?? 0) }} تومان</p>
                                        </td>
                                    </tr>
                                </table>
                                
                                @if($cartItems && $cartItems->count() > 0)
                                <button type="submit" class="btn btn-dark btn-rounded btn-checkout">
                                    ثبت سفارش
                                </button>
                                @endif
                            </div>
                        </form>
                    </div>
                </aside>
            </div>
        </div>
    </div>
</main>

<script>
// تنظیم CSRF token
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

// بارگذاری استان‌ها
$(document).ready(function() {
    loadProvinces();
    
    // تغییر روش ارسال
    $('input[name="shipping_method"]').change(function() {
        calculateShipping();
    });
    
    // تغییر استان
    $('#province').change(function() {
        const province = $(this).val();
        if (province) {
            loadCities(province);
        }
        calculateShipping();
    });
    
    // تغییر شهر
    $('#city').change(function() {
        calculateShipping();
    });
    
    // ثبت سفارش
    $('#checkout-form').submit(function(e) {
        e.preventDefault();
        checkout();
    });
});

function loadProvinces() {
    const provinces = [
        'آذربایجان شرقی', 'آذربایجان غربی', 'اردبیل', 'اصفهان', 'ایلام', 'بوشهر', 'تهران',
        'چهارمحال و بختیاری', 'خراسان جنوبی', 'خراسان رضوی', 'خراسان شمالی', 'خوزستان',
        'زنجان', 'سمنان', 'سیستان و بلوچستان', 'فارس', 'قزوین', 'قم', 'کردستان', 'کرمان',
        'کرمانشاه', 'کهگیلویه و بویراحمد', 'گلستان', 'گیلان', 'لرستان', 'مازندران', 'مرکزی',
        'هرمزگان', 'همدان', 'یزد'
    ];
    
    provinces.forEach(function(province) {
        $('#province').append(`<option value="${province}">${province}</option>`);
    });
}

function loadCities(province) {
    const cities = {
        'تهران': ['تهران', 'ری', 'شهریار', 'ورامین', 'رباط کریم', 'ملارد', 'قدس', 'اسلامشهر', 'پاکدشت'],
        'فارس': ['شیراز', 'مرودشت', 'کازرون', 'جهرم', 'فسا', 'داراب', 'لار', 'آباده', 'فیروزآباد'],
        'اصفهان': ['اصفهان', 'کاشان', 'نجف آباد', 'خمینی شهر', 'شاهین شهر', 'فولادشهر', 'مبارکه', 'نطنز']
    };
    
    $('#city').empty().append('<option value="">انتخاب شهر</option>');
    
    if (cities[province]) {
        cities[province].forEach(function(city) {
            $('#city').append(`<option value="${city}">${city}</option>`);
        });
    }
}

function calculateShipping() {
    const shippingMethod = $('input[name="shipping_method"]:checked').val();
    const shippingCosts = {
        'in_person': 0,
        'tipax': 50000,
        'post_express': 30000
    };
    
    const shippingCost = shippingCosts[shippingMethod] || 0;
    const subtotal = {{ $subtotal ?? 0 }};
    const total = subtotal + shippingCost;
    
    $('.summary-shipping-price').text(shippingCost.toLocaleString() + ' تومان');
    $('.summary-total-price').text(total.toLocaleString() + ' تومان');
}

function updateQuantity(cartId, change) {
    const input = $(`input[data-cart-id="${cartId}"]`);
    const currentValue = parseInt(input.val());
    const newValue = Math.max(1, currentValue + change);
    
    input.val(newValue);
    updateCartItem(cartId, newValue);
}

function updateCartItem(cartId, count) {
    $.ajax({
        url: '{{ route("cart.update") }}',
        method: 'POST',
        data: {
            cart_id: cartId,
            count: count
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            }
        },
        error: function() {
            alert('خطا در به‌روزرسانی سبد خرید');
        }
    });
}

function removeFromCart(cartId) {
    if (confirm('آیا از حذف این محصول اطمینان دارید؟')) {
        $.ajax({
            url: '{{ route("cart.remove") }}',
            method: 'POST',
            data: {
                cart_id: cartId
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                }
            },
            error: function() {
                alert('خطا در حذف محصول');
            }
        });
    }
}

function checkout() {
    const formData = $('#checkout-form').serialize();
    
    $.ajax({
        url: '{{ route("cart.checkout") }}',
        method: 'POST',
        data: formData,
        success: function(response) {
            if (response.success) {
                alert('سفارش شما با موفقیت ثبت شد!');
                window.location.href = '{{ route("account") }}';
            }
        },
        error: function(xhr) {
            const errors = xhr.responseJSON?.errors;
            if (errors) {
                let errorMessage = 'خطاهای زیر رخ داده است:\n';
                Object.values(errors).forEach(function(error) {
                    errorMessage += '- ' + error[0] + '\n';
                });
                alert(errorMessage);
            } else {
                alert('خطا در ثبت سفارش');
            }
        }
    });
}
</script>
@endsection