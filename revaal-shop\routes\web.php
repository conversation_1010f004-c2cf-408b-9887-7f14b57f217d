<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Cache;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\Admin\AdminProductController;
use App\Http\Controllers\Admin\AdminProductCategoryController;
use App\Http\Controllers\Admin\AdminBlogCategoryController;
use App\Http\Controllers\Admin\AdminBlogController;
use App\Http\Controllers\Admin\AdminUserController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\Admin\AdminOrderController;
use App\Http\Controllers\Admin\AdminOfferController;
use App\Http\Controllers\Admin\AdminGalleryController;
use App\Http\Controllers\Admin\AdminSettingController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\AdminRequestController;
use App\Http\Controllers\RequestController;
use App\Http\Controllers\WishlistController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\LocationController;


// Route های صفحات اصلی وبسایت
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/shop', [HomeController::class, 'shop'])->name('shop');
Route::get('/products', [HomeController::class, 'shop'])->name('products');
Route::get('/product/{id}', [HomeController::class, 'product'])->name('product.show');
Route::get('/product', [HomeController::class, 'product'])->name('product.index');
Route::get('/login', [\App\Http\Controllers\AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [\App\Http\Controllers\AuthController::class, 'login'])->name('login.post');
Route::get('/register', [\App\Http\Controllers\AuthController::class, 'showLogin'])->name('register');
Route::post('/register', [\App\Http\Controllers\AuthController::class, 'register'])->name('register.post');
Route::post('/logout', [\App\Http\Controllers\AuthController::class, 'logout'])->name('logout');

// صفحه حساب کاربری فقط برای کاربران لاگین شده
Route::get('/account', function () {
    return view('account');
})->middleware('auth')->name('account');

// Route های بلاگ - ترتیب مهم است (route های خاص قبل از {slug})
Route::prefix('blog')->name('blog.')->group(function () {
    Route::get('/', [BlogController::class, 'index'])->name('index');
    Route::get('/search', [BlogController::class, 'search'])->name('search');
    Route::get('/popular', [BlogController::class, 'popular'])->name('popular');
    Route::get('/rss', [BlogController::class, 'rss'])->name('rss');
    Route::get('/sitemap.xml', [BlogController::class, 'sitemap'])->name('sitemap');
    Route::get('/category/{categoryId}', [BlogController::class, 'category'])->name('category');
    Route::get('/{slug}', [BlogController::class, 'show'])->name('show');
});
Route::get('/about-us', [HomeController::class, 'about'])->name('about-us');
Route::get('/contact-us', [HomeController::class, 'contact'])->name('contact-us');
// Cart routes
Route::get('/cart', [CartController::class, 'index'])->name('cart.index');
Route::post('/cart/add', [CartController::class, 'addToCart'])->name('cart.add');
Route::post('/cart/update', [CartController::class, 'updateCart'])->name('cart.update');
Route::post('/cart/remove', [CartController::class, 'removeFromCart'])->name('cart.remove');
Route::post('/cart/calculate-shipping', [CartController::class, 'calculateShipping'])->name('cart.calculate-shipping');
Route::post('/cart/checkout', [CartController::class, 'checkout'])->name('cart.checkout');

// Location routes
Route::get('/api/provinces', [LocationController::class, 'getProvinces'])->name('api.provinces');
Route::get('/api/cities', [LocationController::class, 'getCities'])->name('api.cities');
Route::get('/wishlist', [HomeController::class, 'wishlist'])->name('wishlist');
// احراز هویت
Route::post('/login', [\App\Http\Controllers\AuthController::class, 'login'])->name('login.post');
Route::post('/register', [\App\Http\Controllers\AuthController::class, 'register'])->name('register.post');
Route::view('/faq', 'faq')->name('faq');

// درخواست مشاوره
Route::get('/request', function () {
    return view('request-form');
})->name('request.form');
Route::post('/request', [RequestController::class, 'store'])->name('request.store');

Route::resource('wishlist', WishlistController::class)->only(['index', 'store', 'destroy']);

Route::get('/account', function () {
    $user = auth()->user();
    return view('account', compact('user'));
})->middleware('auth')->name('account');

Route::get('/admin/dashboard', [\App\Http\Controllers\Admin\AdminDashboardController::class, 'index'])->name('admin.dashboard');

Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    Route::resource('products', AdminProductController::class);
    Route::resource('product-categories', AdminProductCategoryController::class);
    Route::resource('blog-categories', AdminBlogCategoryController::class);
    // مدیریت بلاگ
    Route::resource('blogs', AdminBlogController::class);
    Route::patch('blogs/{blog}/toggle-published', [AdminBlogController::class, 'togglePublished'])->name('blogs.toggle-published');
    Route::patch('blogs/{blog}/toggle-featured', [AdminBlogController::class, 'toggleFeatured'])->name('blogs.toggle-featured');
    Route::post('blogs/bulk-action', [AdminBlogController::class, 'bulkAction'])->name('blogs.bulk-action');
    Route::post('blogs/upload-image', [AdminBlogController::class, 'uploadImage'])->name('blogs.upload-image');
    Route::patch('blogs/{id}/restore', [AdminBlogController::class, 'restore'])->name('blogs.restore');
    Route::delete('blogs/{id}/force-delete', [AdminBlogController::class, 'forceDelete'])->name('blogs.force-delete');

    // مدیریت کاربران
    Route::get('/users', [AdminUserController::class, 'index'])->name('users.index');
    Route::get('/users/{user}', [AdminUserController::class, 'show'])->name('users.show');
    Route::patch('/users/{user}/toggle-admin', [AdminUserController::class, 'toggleAdmin'])->name('users.toggle-admin');
    Route::patch('/users/{user}/update-wallet', [AdminUserController::class, 'updateWallet'])->name('users.update-wallet');

    // مدیریت سفارشات
    Route::get('/orders', [AdminOrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/{order}', [AdminOrderController::class, 'show'])->name('orders.show');
    Route::patch('/orders/{order}/update-status', [AdminOrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::patch('/orders/{order}/update-notes', [AdminOrderController::class, 'updateNotes'])->name('orders.update-notes');

    // مدیریت کدهای تخفیف
    Route::resource('offers', AdminOfferController::class);

    // روت‌های گالری
    Route::get('/galleries', [AdminGalleryController::class, 'index'])->name('galleries.index');
    Route::post('/galleries', [AdminGalleryController::class, 'store'])->name('galleries.store');
    Route::delete('/galleries/{gallery}', [AdminGalleryController::class, 'destroy'])->name('galleries.destroy');

    // مدیریت تنظیمات
    Route::get('/settings', [AdminSettingController::class, 'index'])->name('settings.index');
    Route::post('/settings', [AdminSettingController::class, 'update'])->name('settings.update');

    // مدیریت درخواست‌های مشاوره
    Route::resource('requests', AdminRequestController::class)->only(['index', 'show', 'update', 'destroy']);

});

Route::get('/test', function () {
    return view('test');
});
