<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductDetail extends Model
{
    protected $fillable = [
        'product_id',
        'dimension',
        'country',
        'suitable_for',
        'manufacturer',
        'introduction',
        'introduction_video',
        'pexpline',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
