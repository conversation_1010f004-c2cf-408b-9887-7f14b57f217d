<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Exception;

class AuthController extends Controller
{
    public function showLogin()
    {
        return view('login');
    }

    public function login(Request $request)
    {
        try {
            $credentials = $request->validate([
                'singin-email' => 'required|string',
                'singin-password' => 'required|string',
            ]);

            $loginField = $credentials['singin-email'];
            $password = $credentials['singin-password'];

            Log::info('Login attempt', ['field' => $loginField]);

            // Try to find user by email or username
            $user = User::where('email', $loginField)
                       ->orWhere('name', $loginField)
                       ->first();

            if (!$user) {
                Log::warning('User not found', ['field' => $loginField]);
                return back()->withErrors(['login_error' => 'کاربر یافت نشد.'])->withInput();
            }

            if (!Hash::check($password, $user->password)) {
                Log::warning('Password mismatch', ['user_id' => $user->id]);
                return back()->withErrors(['login_error' => 'رمز عبور اشتباه است.'])->withInput();
            }

            Auth::login($user);
            Log::info('User logged in successfully', ['user_id' => $user->id]);
            
            // Redirect based on role
            if ($user->role === 'admin') {
                return redirect()->route('admin.dashboard');
            } else {
                return redirect()->route('account');
            }
        } catch (Exception $e) {
            Log::error('Login error: ' . $e->getMessage());
            return back()->withErrors(['login_error' => 'مشکلی پیش آمد. لطفاً دوباره تلاش کنید.'])->withInput();
        }
    }

    public function register(Request $request)
    {
        try {
            Log::info('Registration attempt', $request->only(['register-name', 'register-email', 'register-phone']));
            
            $validator = Validator::make($request->all(), [
                'register-name' => 'required|string|max:255|unique:users,name',
                'register-email' => 'required|string|email|max:255|unique:users,email',
                'register-phone' => 'required|string|max:20',
                'register-password' => 'required|string|min:6',
                'register-agree' => 'required|accepted',
            ], [
                'register-name.required' => 'نام کاربری الزامی است.',
                'register-name.unique' => 'این نام کاربری قبلاً استفاده شده است.',
                'register-email.required' => 'ایمیل الزامی است.',
                'register-email.email' => 'فرمت ایمیل صحیح نیست.',
                'register-email.unique' => 'این ایمیل قبلاً ثبت شده است.',
                'register-phone.required' => 'شماره تلفن الزامی است.',
                'register-password.required' => 'رمز عبور الزامی است.',
                'register-password.min' => 'رمز عبور باید حداقل 6 کاراکتر باشد.',
                'register-agree.required' => 'پذیرش قوانین الزامی است.',
            ]);

            if ($validator->fails()) {
                Log::warning('Validation failed', $validator->errors()->toArray());
                return back()->withErrors($validator)->withInput();
            }

            $userData = [
                'name' => $request->input('register-name'),
                'email' => $request->input('register-email'),
                'phone' => $request->input('register-phone'),
                'password' => Hash::make($request->input('register-password')),
                'role' => 'user',
            ];

            Log::info('Creating user', ['name' => $userData['name'], 'email' => $userData['email']]);
            
            $user = User::create($userData);
            
            Log::info('User created successfully', ['user_id' => $user->id]);

            Auth::login($user);
            
            Log::info('User logged in after registration', ['user_id' => $user->id]);
            
            // همه کاربران بعد از ثبت‌نام به صفحه account می‌روند
            return redirect()->route('account');
        } catch (Exception $e) {
            Log::error('Registration error: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return back()->withErrors(['ثبت‌نام با مشکل مواجه شد. لطفاً دوباره تلاش کنید.'])->withInput();
        }
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('login');
    }
}