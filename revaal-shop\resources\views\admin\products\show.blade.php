@extends('admin.app')
@section('title', 'جزئیات محصول')

@section('content')
<div class="row">
    <div class="col-xl-12">
                        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title">جزئیات محصول</h4>
                <a href="{{ route('admin.products.index') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right"></i>
                    بازگشت به لیست محصولات
                </a>
            </div>
                            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6">
                        <h5>نام محصول</h5>
                        <p>{{ $product->name }}</p>
                    </div>
                    <div class="col-lg-6">
                        <h5>دسته‌بندی</h5>
                        <p>{{ $product->category->name ?? 'بدون دسته‌بندی' }}</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <h5>قیمت (تومان)</h5>
                        <p>{{ number_format($product->price) }}</p>
                                </div>
                    <div class="col-lg-6">
                        <h5>قیمت بدون تخفیف (تومان)</h5>
                        <p>{{ number_format($product->no_offer_price) }}</p>
                                            </div>
                                        </div>
                <div class="row">
                    <div class="col-lg-6">
                        <h5>موجودی</h5>
                        <p>{{ $product->inventory }}</p>
                                    </div>
                    <div class="col-lg-6">
                        <h5>نوع محصول</h5>
                        <p>{{ $product->type }}</p>
                                            </div>
                                        </div>
                <div class="row">
                    <div class="col-lg-12">
                        <h5>توضیحات</h5>
                        <p>{{ $product->description }}</p>
                                    </div>
                                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <h5>تصویر اصلی محصول</h5>
                        <div class="text-center">
                            <img src="{{ asset($product->image) }}" alt="{{ $product->name }}" class="img-fluid rounded" style="max-height: 400px;">
                                    </div>
                                </div>
                                </div>
                @if($product->other_image)
                <div class="row mt-4">
                    <div class="col-lg-12">
                        <h5>تصاویر دیگر</h5>
                        <div class="row">
                            @foreach(json_decode($product->other_image) as $image)
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                <div class="card">
                                    <img src="{{ asset($image) }}" alt="{{ $product->name }}" class="card-img-top" style="height: 200px; object-fit: cover;">
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
                <div class="row">
                    <div class="col-lg-6">
                        <h5>وضعیت</h5>
                        <p>
                            <span class="badge bg-{{ $product->status == 'active' ? 'success' : 'danger' }}">
                                {{ $product->status == 'active' ? 'فعال' : 'غیرفعال' }}
                            </span>
                        </p>
                    </div>
                    <div class="col-lg-6">
                        <h5>محصول ویژه</h5>
                        <p>
                            <span class="badge bg-{{ $product->is_special ? 'warning' : 'secondary' }}">
                                {{ $product->is_special ? 'بله' : 'خیر' }}
                            </span>
                        </p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <h5>توضیحات متا</h5>
                        <p>{{ $product->meta_description }}</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <h5>کلمات کلیدی</h5>
                        <p>{{ $product->meta_keywords }}</p>
                    </div>
                </div>

                @if($product->detail)
                <div class="row mt-4">
                    <div class="col-lg-12">
                        <h5>مشخصات تکمیلی محصول</h5>
                        <ul class="list-group mb-3">
                            <li class="list-group-item"><strong>ابعاد:</strong> {{ $product->detail->dimension ?? '-' }}</li>
                            <li class="list-group-item"><strong>کشور سازنده:</strong> {{ $product->detail->country ?? '-' }}</li>
                            <li class="list-group-item"><strong>مناسب برای:</strong> {{ $product->detail->suitable_for ?? '-' }}</li>
                            <li class="list-group-item"><strong>تولید کننده:</strong> {{ $product->detail->manufacturer ?? '-' }}</li>
                            <li class="list-group-item"><strong>توضیح کوتاه معرفی:</strong> {{ $product->detail->pexpline ?? '-' }}</li>
                            <li class="list-group-item"><strong>معرفی محصول:</strong> {!! nl2br(e($product->detail->introduction)) ?? '-' !!}</li>
                            <li class="list-group-item"><strong>ویدیو معرفی:</strong> @if($product->detail->introduction_video)
                                <a href="{{ $product->detail->introduction_video }}" target="_blank">مشاهده ویدیو</a>
                                @else - @endif
                            </li>
                        </ul>
                    </div>
                </div>
                @endif

                @if($product->options && $product->options->count() > 0)
                <div class="row mt-4">
                    <div class="col-lg-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>گزینه‌های محصول</h5>
                            <a href="{{ route('admin.products.edit', $product->id) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i> ویرایش گزینه‌ها
                            </a>
                        </div>
                        <div class="d-flex flex-wrap gap-3">
                            @foreach($product->options as $option)
                                <div class="border rounded p-3 d-flex flex-column align-items-center" style="min-width:200px; min-height:180px; background:#f9f9f9; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                    <div class="mb-2 fw-bold text-center">{{ $option->name }}</div>
                                    <div class="mb-2 text-center">
                                        @if($option->color_name)
                                            <span class="d-inline-flex align-items-center">
                                                @if($option->color_code)
                                                    <span style="display:inline-block;width:20px;height:20px;background:{{ $option->color_code }};border-radius:50%;border:2px solid #ddd;margin-left:8px;"></span>
                                                @endif
                                                <span class="badge bg-light text-dark">{{ $option->color_name }}</span>
                                            </span>
                                        @else
                                            <span class="text-muted">بدون رنگ</span>
                                        @endif
                                    </div>
                                    <div class="mb-2 text-center">
                                        @if($option->size)
                                            <span class="badge bg-info fs-6">{{ strtoupper($option->size) }}</span>
                                        @else
                                            <span class="text-muted">بدون سایز</span>
                                        @endif
                                    </div>
                                    <div class="mb-2 text-center">
                                        <span class="text-secondary">قیمت:</span> 
                                        <span class="fw-bold text-success">{{ number_format($option->price) }} تومان</span>
                                    </div>
                                    <div class="mb-2 text-center">
                                        <span class="text-secondary">موجودی:</span> 
                                        <span class="badge bg-{{ $option->inventory > 0 ? 'success' : 'danger' }} fs-6">{{ $option->inventory }}</span>
                                    </div>
                                    @if($option->note)
                                        <div class="mb-2 text-center">
                                            <span class="text-secondary">یادداشت:</span> 
                                            <span class="small">{{ $option->note }}</span>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @else
                
                <div class="row mt-4">
                    <div class="col-lg-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>گزینه‌های محصول</h5>
                            <a href="{{ route('admin.products.edit', $product->id) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> افزودن گزینه
                            </a>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            این محصول هیچ گزینه‌ای ندارد. برای افزودن گزینه‌های مختلف (سایز، رنگ و ...) روی دکمه "افزودن گزینه" کلیک کنید.
                        </div>
                    </div>
                </div>
                @endif

            </div>
                            </div>
                        </div>
                    </div>
@endsection