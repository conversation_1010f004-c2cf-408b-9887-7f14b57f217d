@extends('app')
@section('title', 'جزئیات محصول')
@section('style')
<link rel="stylesheet" type="text/css" href="{{ asset('css/demo4.min.css') }}">
<link rel="stylesheet" type="text/css" href="{{ asset('css/style.min.css') }}">
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection
@section('content')
<main class="allpromain main single-product rtl text-right">
            <div class="sigleproduct page-content mb-10 pb-6">
                <div class="container">
                    <div class="product rtl product-single row mb-7">
                        <div class="col-md-6 sticky-sidebar-wrapper">
                            <div class="product-gallery ltr pg-vertical sticky-sidebar"
                                 data-sticky-options="{'minWidth': 767}">
                                <div class="product-single-carousel owl-carousel owl-theme owl-nav-inner row cols-1 gutter-no ltr">
                                    @php
                                        $otherImages = is_array($product->other_image) ? $product->other_image : (json_decode($product->other_image, true) ?? []);
                                    @endphp
                                    <figure class="product-image">
                                        <img src="{{ asset($product->image) }}"
                                             data-zoom-image="{{ asset($product->image) }}"
                                             alt="{{ $product->name }}" width="800" height="900">
                                    </figure>
                                    @foreach($otherImages as $img)
                                        <figure class="product-image">
                                            <img src="{{ asset($img) }}"
                                                 data-zoom-image="{{ asset($img) }}"
                                                 alt="{{ $product->name }}" width="800" height="900">
                                        </figure>
                                    @endforeach
                                </div>
                                <div class="product-thumbs-wrap ltr">
                                    <div class="product-thumbs">
                                        <div class="product-thumb">
                                            <img src="{{ asset($product->image) }}" alt="product thumbnail"
                                                 width="109" height="122">
                                        </div>
                                        @foreach($otherImages as $img)
                                            <div class="product-thumb">
                                                <img src="{{ asset($img) }}" alt="product thumbnail"
                                                     width="109" height="122">
                                            </div>
                                        @endforeach
                                    </div>
                                    <button class="thumb-up disabled"><i class="fas fa-chevron-right"></i></button>
                                    <button class="thumb-down disabled"><i class="fas fa-chevron-left"></i></button>
                                </div>
                                <div class="product-label-group">
                                    <label class="product-label label-new">جدید</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="product-details  rtl text-right">
                                <div class="product-navigation">
                                    <ul class="breadcrumb breadcrumb-lg">
                                        <li><a href="demo1.html"><i class="d-icon-home"></i></a></li>
                                        <li><a href="#" class="active">محصول</a></li>
                                        <li>محصول</li>
                                    </ul>
                                    <ul class="product-nav">
                                        <li class="product-nav-prev">
                                            @if(isset($prevProduct) && $prevProduct)
                                                <a href="{{ route('product.show', $prevProduct->id) }}">
                                                    <i class="d-icon-arrow-right"></i> قبلی
                                                    <span class="product-nav-popup">
                                                        <img src="{{ $prevProduct->image ? asset($prevProduct->image) : asset('images/products/default.png') }}"
                                                             alt="product thumbnail" width="110" height="123">
                                                        <span class="product-name">{{ $prevProduct->name }}</span>
                                                    </span>
                                                </a>
                                            @else
                                                <a href="#" class="disabled" tabindex="-1" aria-disabled="true">
                                                    <i class="d-icon-arrow-right"></i> قبلی
                                                </a>
                                            @endif
                                        </li>
                                        <li class="product-nav-next">
                                            @if(isset($nextProduct) && $nextProduct)
                                                <a href="{{ route('product.show', $nextProduct->id) }}">
                                                   بعدی <i class="d-icon-arrow-left"></i>
                                                    <span class="product-nav-popup">
                                                        <img src="{{ $nextProduct->image ? asset($nextProduct->image) : asset('images/products/default.png') }}"
                                                             alt="product thumbnail" width="110" height="123">
                                                        <span class="product-name">{{ $nextProduct->name }}</span>
                                                    </span>
                                                </a>
                                            @else
                                                <a href="#" class="disabled" tabindex="-1" aria-disabled="true">
                                                    بعدی <i class="d-icon-arrow-left"></i>
                                                </a>
                                            @endif
                                        </li>
                                    </ul>
                                </div>
                                <h1 class="product-namee">{{ $product->name }}</h1>
                                <div class="product-meta">
                                    شناسه: <span class="product-sku">{{ $product->id }}</span>
                                    برند: <span class="product-brand">{{ $product->brand ?? '-' }}</span>
                                </div>
                                <div class="product-pricee">{{ number_format($product->price) }} تومان</div>
                                <div class="ratings-containerr">
                                    <div class="ratings-full">
                                        <span class="ratings" style="width:{{ $product->average_rating ?? 0 }}%"></span>
                                        <span class="tooltiptext tooltip-top"></span>
                                    </div>
                                    <a href="#product-tab-reviews" class="link-to-tab rating-reviews">( از {{ $product->reviews_count ?? 0 }} نظر )</a>
                                </div>
                                <p class="product-short-desc">
                                    {{ $product->description }}
                                </p>
                                <div class="product-form product-color">
									<label>رنگ:</label>
									<div class="product-variations">
                                        @if($product->options && $product->options->count())
                                            @foreach($product->options->unique('color_code') as $option)
                                                @if($option->color_code)
                                                    <a class="color" data-src="{{ asset($product->image) }}" href="#"
                                                       style="background-color: {{ $option->color_code }}" title="{{ $option->color_name }}"></a>
                                                @endif
                                            @endforeach
                                        @else
                                            <span class="text-muted">بدون رنگ</span>
                                        @endif
									</div>
								</div>
                                <div class="product-form product-variations product-size">
                                    <label>سایزبندی:</label>
                                    <div class="product-form-group">
                                        <div class="select-box">
                                            <select name="size" class="form-control">
                                                <option value="" selected="selected">یک گزینه را انتخاب کنید</option>
                                                @if($product->options && $product->options->count())
                                                    @foreach($product->options->unique('size') as $option)
                                                        @if($option->size)
                                                            <option value="{{ $option->size }}">{{ strtoupper($option->size) }}</option>
                                                        @endif
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                        <a href="#" class="product-variation-clean" style="display: none;">پاک کردن</a>
                                    </div>
                                </div>
                                <div class="product-variation-price">
                                    <span>180,000 تومان</span>
                                </div>
                                <hr class="product-divider">
                                <form id="add-to-cart-form" class="product-form product-qty">
                                    @csrf
                                    <input type="hidden" name="product_id" value="{{ $product->id }}">
                                    <div class="product-form-group">
                                        <div class="input-group ml-2">
                                            <button type="button" class="quantity-minus d-icon-minus"></button>
                                            <input class="quantity form-control" name="quantity" type="number" min="1" max="1000000" value="1">
                                            <button type="button" class="quantity-plus d-icon-plus"></button>
                                        </div>
                                        <button type="submit" class="btn-product btn-cart text-normal ls-normal font-weight-semi-bold">
                                            <i class="d-icon-bag"></i> افزودن به سبد خرید
                                        </button>
                                    </div>
                                </form>
                                <hr class="product-divider mb-3">
                                <div class="product-footer">
                                    <div class="social-links ml-4">
                                        <a href="#" class="social-link social-facebook fab fa-facebook-f"></a>
                                        <a href="#" class="social-link social-twitter fab fa-twitter"></a>
                                        <a href="#" class="social-link social-pinterest fab fa-pinterest-p"></a>
                                    </div>
                                    <span class="divider d-lg-show"></span>
                                    <a href="#" class="btn-product btn-wishlist ml-6"
                                       onclick="event.preventDefault(); document.getElementById('wishlist-form-{{ $product->id }}').submit();">
                                        <i class="d-icon-heart"></i> اضافه به علاقه مندی
                                    </a>
                                    <form id="wishlist-form-{{ $product->id }}" action="{{ route('wishlist.store') }}" method="POST" style="display:none;">
                                        @csrf
                                        <input type="hidden" name="product_id" value="{{ $product->id }}">
                                    </form>
                                    <a href="#" class="btn-product btn-compare">
                                        <i class="d-icon-compare"></i> اضافه به مقایسه
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab tab-nav-simple product-tabs  rtl text-right">
                        <ul class="nav nav-tabs justify-content-center" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" href="#product-tab-description">معرفی محصول</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#product-tab-additional">مشخصات فنی</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#product-tab-size-guide">راهنمای سایز</a>
                            </li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane active in" id="product-tab-description">
                                <div class="row mt-6">
                                    <div class="col-md-6">
                                        @if($product->detail && $product->detail->introduction)
                                            <p class="pexpline mb-2">{{ $product->detail->introduction }}</p>
                                        @endif
                                        @if($product->detail && $product->detail->pexpline)
                                        <ul class="mb-8">
                                                @foreach(explode("\n", $product->detail->pexpline) as $line)
                                                    @if(trim($line) !== '')
                                                        <li>{{ $line }}</li>
                                                    @endif
                                                @endforeach
                                        </ul>
                                        @endif
                                    </div>
                                    <div class="col-md-6 pl-md-6 pt-4 pt-md-0">
                                        <figure class="p-relative d-inline-block mb-2">
                                            @if($product->detail && $product->detail->introduction_video)
                                                <video width="100%" height="auto" controls>
                                                    <source src="{{ asset($product->detail->introduction_video) }}" type="video/mp4">
                                                    مرورگر شما از پخش ویدیو پشتیبانی نمی‌کند.
                                                </video>
                                            @endif
                                        </figure>
                                        <div class="icon-box-wrap d-flex flex-wrap">
                                            <div class="icon-box icon-box-side icon-border pt-2 pb-2 mb-4 ml-10">
                                                <div class="icon-box-icon">
                                                    <i class="d-icon-lock"></i>
                                                </div>
                                                <div class="icon-box-content rtl">
                                                    <h4 class="icon-box-title lh-1 pt-1 ls-s text-normal">
                                                        2 سال گارانتی
                                                    </h4>
                                                    <p>بی قید و شرط</p>
                                                </div>
                                            </div>
                                            <div class="divider d-xl-show ml-11"></div>
                                            <div class="icon-box icon-box-side icon-border pt-2 pb-2 mb-4">
                                                <div class="icon-box-icon">
                                                    <i class="d-icon-truck"></i>
                                                </div>
                                                <div class="icon-box-content rtl">
                                                    <h4 class="icon-box-title lh-1 pt-1 ls-s text-normal">
                                                        ارسال رایگان
                                                    </h4>
                                                    <p>خرید منی بیشتر از 300 هزار تومان</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane" id="product-tab-additional">
                                <table class="table">
                                    <tbody>
                                        <tr>
                                            <th class="font-weight-semi-bold text-dark pl-0">ابعاد</th>
                                            <td class="pr-4">{{ $product->detail && $product->detail->dimension ? $product->detail->dimension : '' }}</td>
                                        </tr>
                                        <tr>
                                            <th class="font-weight-semi-bold text-dark pl-0">کشور سازنده :</th>
                                            <td class="pr-4">{{ $product->detail && $product->detail->country ? $product->detail->country : '' }}</td>
                                        </tr>
                                        <tr>
                                            <th class="font-weight-semi-bold text-dark pl-0">مناسب برای</th>
                                            <td class="pr-4">{{ $product->detail && $product->detail->suitable_for ? $product->detail->suitable_for : '' }}</td>
                                        </tr>
                                        <tr>
                                            <th class="font-weight-semi-bold text-dark border-no pl-0">تولید کننده</th>
                                            <td class="border-no pl-4">{{ $product->detail && $product->detail->manufacturer ? $product->detail->manufacturer : '' }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="tab-pane " id="product-tab-size-guide">
                                <figure class="size-image mt-4 mb-4">
                                    <img src="asset{{('images')}}/product/size_guide.png" alt="Size Guide Image" width="217"
                                         height="398">
                                </figure>
                                <figure class="size-table mt-4 mb-4">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>سایز</th>
                                                <th>دور سینه</th>
                                                <th>عرض شانه</th>
                                                <th>دور سینه</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <th>XS</th>
                                                <td>34-36</td>
                                                <td>27-29</td>
                                                <td>34.5-36.5</td>
                                            </tr>
                                            <tr>
                                                <th>S</th>
                                                <td>36-38</td>
                                                <td>29-31</td>
                                                <td>36.5-38.5</td>
                                            </tr>
                                            <tr>
                                                <th>M</th>
                                                <td>38-40</td>
                                                <td>31-33</td>
                                                <td>38.5-40.5</td>
                                            </tr>
                                            <tr>
                                                <th>L</th>
                                                <td>40-42</td>
                                                <td>33-36</td>
                                                <td>40.5-43.5</td>
                                            </tr>
                                            <tr>
                                                <th>XL</th>
                                                <td>42-45</td>
                                                <td>36-40</td>
                                                <td>43.5-47.5</td>
                                            </tr>
                                            <tr>
                                                <th>XXL</th>
                                                <td>45-48</td>
                                                <td>40-44</td>
                                                <td>47.5-51.5</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </figure>
                            </div>
                        </div>
                    </div>
                    <section class="pt-3 mt-10">
                        <h2 class="title justify-content-center">محصولات مرتبط</h2>
                        <div class="owl-carousel owl-theme owl-nav-full row cols-2 cols-md-3 cols-lg-4"
                             data-owl-options="{
                             'rtl':true,
							'items': 5,
							'nav': false,
							'loop': false,
							'dots': true,
							'margin': 20,
							'responsive': {
								'0': {
									'items': 2
								},
								'768': {
									'items': 3
								},
								'992': {
									'items': 4,
									'dots': false,
									'nav': true
								}
							}
						}">
                            @foreach($relatedProducts as $related)
                            <div class="product">
                                <figure class="product-media">
                                    <a href="{{ route('product.show', $related->id) }}">
                                        <img src="{{ $related->image ? asset($related->image) : asset('images/products/default.png') }}" alt="product" width="280" height="315">
                                    </a>
                                    <div class="product-label-group">
                                        <label class="product-label label-new">جدید</label>
                                    </div>
                                    <div class="product-action-vertical">
                                        <a href="#" class="btn-product-icon btn-cart" data-toggle="modal"
                                           data-target="#addCartModal" title="افزودن به سبد خرید">
                                            <i class="d-icon-bag"></i>
                                        </a>
                                        <a href="#" class="btn-product-icon btn-wishlist" title="اضافه به علاقه مندی">
                                            <i class="d-icon-heart"></i>
                                        </a>
                                    </div>
                                    <div class="product-action">
                                        <a href="{{ route('product.show', $related->id) }}" class="btn-product btn-quickview" title="نمایش ">مشاهده </a>
                                    </div>
                                </figure>
                                <div class="product-details rtl">
                                    <div class="product-cat">
                                        <a href="#">{{ $related->category ? $related->category->name : '' }}</a>
                                    </div>
                                    <h3 class="product-name">
                                        <a href="{{ route('product.show', $related->id) }}">{{ $related->name }}</a>
                                    </h3>
                                    <div class="product-price">
                                        <span class="price">{{ number_format($related->price) }} تومان</span>
                                    </div>
                                    <div class="ratings-container">
                                        <div class="ratings-full">
                                            <span class="ratings" style="width:{{ $related->average_rating ?? 0 }}%"></span>
                                            <span class="tooltiptext tooltip-top"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </section>
                </div>
            </div>
        </main>
        @section('script')
        <script src="vendor/photoswipe/photoswipe-ui-default.min.js"></script>
        <script src="vendor/photoswipe/photoswipe.min.js"></script>
        <script src="vendor/sticky/sticky.min.js"></script>
        <script>
        $(document).ready(function() {
            // Setup CSRF token for AJAX requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Quantity buttons
            $('.quantity-plus').click(function() {
                var input = $(this).siblings('.quantity');
                var currentVal = parseInt(input.val());
                if (!isNaN(currentVal)) {
                    input.val(currentVal + 1);
                }
            });

            $('.quantity-minus').click(function() {
                var input = $(this).siblings('.quantity');
                var currentVal = parseInt(input.val());
                if (!isNaN(currentVal) && currentVal > 1) {
                    input.val(currentVal - 1);
                }
            });

            // Add to cart form submission
            $('#add-to-cart-form').submit(function(e) {
                e.preventDefault();
                
                var formData = {
                    product_id: $('input[name="product_id"]').val(),
                    quantity: $('input[name="quantity"]').val(),
                    _token: $('input[name="_token"]').val()
                };

                $.ajax({
                    url: '{{ route("cart.add") }}',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            alert('محصول با موفقیت به سبد خرید اضافه شد!');
                            
                            // Optional: Update cart count in header if you have one
                            // $('.cart-count').text(response.cart_count);
                        } else {
                            alert('خطا در اضافه کردن محصول: ' + response.message);
                        }
                    },
                    error: function(xhr) {
                        console.log(xhr.responseText);
                        alert('خطا در اضافه کردن محصول به سبد خرید');
                    }
                });
            });
        });
        </script>
        @endsection
@endsection
