<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Review;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'price',
        'no_offer_price',
        'inventory',
        'description',
        'image',
        'other_image',
        'type',
        'meta_description',
        'meta_keywords',
        'is_special',
        'status',
        'product_category',
        'badge',
        'badge_class',
    ];

    protected $casts = [
        'is_special' => 'boolean',
        'other_image' => 'array',
    ];

    public function options()
    {
        return $this->hasMany(ProductOption::class);
    }

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'product_category');
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    public function detail()
    {
        return $this->hasOne(ProductDetail::class);
    }

    public function getAverageRatingAttribute()
    {
        // Return 0 if there are no reviews to avoid division by zero
        if ($this->reviews->count() == 0) {
            return 0;
        }
        return $this->reviews->avg('rating') * 20; // Multiply by 20 to get a percentage for the stars
    }

    public function getReviewsCountAttribute()
    {
        return $this->reviews->count();
    }
}
