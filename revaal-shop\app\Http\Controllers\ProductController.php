<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\ProductCategory;

class ProductController extends Controller
{
    /**
     * نمایش لیست محصولات (صفحه فروشگاه)
     */
    public function index(Request $request)
    {
        $query = Product::with('category')->where('status', 1);

        // فیلتر بر اساس دسته‌بندی
        if ($request->has('category') && $request->category) {
            $query->where('product_category', $request->category);
        }

        // فیلتر بر اساس قیمت
        if ($request->has('min_price') && $request->min_price) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price') && $request->max_price) {
            $query->where('price', '<=', $request->max_price);
        }

        // جستجو در نام محصول
        if ($request->has('search') && $request->search) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // فیلتر محصولات ویژه
        if ($request->has('special') && $request->special) {
            $query->where('is_special', true);
        }

        // مرتب سازی
        $orderBy = $request->get('orderby', 'created_at');
        $order = $request->get('order', 'desc');

        switch ($orderBy) {
            case 'price-low':
                $query->orderBy('price', 'asc');
                break;
            case 'price-high':
                $query->orderBy('price', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'popularity':
                $query->orderBy('is_special', 'desc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        $products = $query->paginate(12);

        // دریافت دسته‌بندی‌ها برای فیلتر
        $categories = ProductCategory::whereNull('parent')
            ->with('children')
            ->get();

        return view('product.index', compact('products', 'categories'));
    }

    /**
     * نمایش جزئیات محصول
     */
    public function show($id)
    {
        $product = Product::with(['category', 'options', 'reviews.user', 'detail'])
            ->where('status', 1)
            ->findOrFail($id);
        
        // نمایش محصولات مشابه
        $relatedProducts = Product::with('category')
            ->where('status', 1)
            ->where('product_category', $product->product_category)
            ->where('id', '!=', $product->id)
            ->orderBy('created_at', 'desc')
            ->take(4)
            ->get();
        
        // قبلی و بعدی
        $prevProduct = Product::where('status', 1)
            ->where('product_category', $product->product_category)
            ->where('id', '<', $product->id)
            ->orderBy('id', 'desc')
            ->first();
        $nextProduct = Product::where('status', 1)
            ->where('product_category', $product->product_category)
            ->where('id', '>', $product->id)
            ->orderBy('id', 'asc')
            ->first();
        
        return view('product.show', compact('product', 'relatedProducts', 'prevProduct', 'nextProduct'));
    }

    /**
     * جستجو در محصولات
     */
    public function search(Request $request)
    {
        $query = $request->get('q');
        
        if (!$query) {
            return redirect()->route('products.index');
        }

        $products = Product::with('category')
            ->where('status', 1)
            ->where(function($q) use ($query) {
                $q->where('name', 'like', '%' . $query . '%')
                  ->orWhere('description', 'like', '%' . $query . '%')
                  ->orWhere('meta_keywords', 'like', '%' . $query . '%');
            })
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        $categories = ProductCategory::whereNull('parent')
            ->with('children')
            ->get();

        return view('product.search', compact('products', 'categories', 'query'));
    }

    /**
     * نمایش محصولات بر اساس دسته‌بندی
     */
    public function category($categoryId)
    {
        $category = ProductCategory::findOrFail($categoryId);
        
        $products = Product::with('category')
            ->where('status', 1)
            ->where('product_category', $categoryId)
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        $categories = ProductCategory::whereNull('parent')
            ->with('children')
            ->get();

        return view('product.category', compact('products', 'categories', 'category'));
    }

    /**
     * نمایش محصولات ویژه
     */
    public function special()
    {
        $products = Product::with('category')
            ->where('status', 1)
            ->where('is_special', true)
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        $categories = ProductCategory::whereNull('parent')
            ->with('children')
            ->get();

        return view('product.special', compact('products', 'categories'));
    }

    /**
     * مقایسه محصولات
     */
    public function compare(Request $request)
    {
        $productIds = $request->get('products', []);
        
        if (empty($productIds) || count($productIds) < 2) {
            return redirect()->route('products.index')
                ->with('error', 'لطفاً حداقل دو محصول برای مقایسه انتخاب کنید.');
        }

        $products = Product::with(['category', 'options'])
            ->where('status', 1)
            ->whereIn('id', $productIds)
            ->get();

        return view('product.compare', compact('products'));
    }

    /**
     * افزودن محصول به علاقه‌مندی‌ها
     */
    public function addToWishlist(Request $request, $productId)
    {
        // این متد بعداً با سیستم احراز هویت کامل می‌شود
        $product = Product::findOrFail($productId);
        
        // فعلاً در session ذخیره می‌کنیم
        $wishlist = session()->get('wishlist', []);
        
        if (!in_array($productId, $wishlist)) {
            $wishlist[] = $productId;
            session()->put('wishlist', $wishlist);
            
            return response()->json([
                'success' => true,
                'message' => 'محصول به علاقه‌مندی‌ها اضافه شد'
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'محصول قبلاً در علاقه‌مندی‌ها موجود است'
        ]);
    }

    /**
     * حذف محصول از علاقه‌مندی‌ها
     */
    public function removeFromWishlist($productId)
    {
        $wishlist = session()->get('wishlist', []);
        
        if (($key = array_search($productId, $wishlist)) !== false) {
            unset($wishlist[$key]);
            session()->put('wishlist', array_values($wishlist));
            
            return response()->json([
                'success' => true,
                'message' => 'محصول از علاقه‌مندی‌ها حذف شد'
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'محصول در علاقه‌مندی‌ها یافت نشد'
        ]);
    }

    /**
     * نمایش علاقه‌مندی‌ها
     */
    public function wishlist()
    {
        $wishlistIds = session()->get('wishlist', []);
        
        $products = Product::with('category')
            ->where('status', 1)
            ->whereIn('id', $wishlistIds)
            ->get();

        return view('product.wishlist', compact('products'));
    }
}
