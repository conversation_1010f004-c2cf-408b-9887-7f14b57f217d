<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductOption;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AdminProductController extends Controller
{
    public function index()
    {
        $products = Product::with(['options', 'category'])->latest()->paginate(12);
        return view('admin.products.index', compact('products'));
    }

    public function show(Product $product)
    {
        $product->load('options');
        return view('admin.products.show', compact('product'));
    }

    public function create()
    {
        $categories = ProductCategory::all();
        return view('admin.products.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric',
            'no_offer_price' => 'nullable|numeric',
            'inventory' => 'required|integer',
            'description' => 'nullable|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'other_image.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'type' => 'required|string|max:255',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string|max:255',
            'is_special' => 'boolean',
            'product_category' => 'required|exists:product_categories,id',
            'badge' => 'nullable|string|max:255',
            'badge_class' => 'nullable|string|max:255',
            'status' => 'boolean',
            'options' => 'nullable|array',
            'options.*.name' => 'nullable|string|max:255',
            'options.*.color_name' => 'nullable|string|max:255',
            'options.*.color_code' => 'nullable|string|max:7',
            'options.*.size' => 'nullable|string|in:s,m,l,xl,2xl,3xl',
            'options.*.price' => 'nullable|numeric|min:0',
            'options.*.inventory' => 'nullable|integer|min:0',
            'options.*.note' => 'nullable|string|max:500',
            'dimension' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'suitable_for' => 'nullable|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'introduction' => 'nullable|string',
            'introduction_video' => 'nullable|string',
            'pexpline' => 'nullable|string',
        ]);

        // تنظیم نوع محصول به سکشن خالی
        $data['type'] = 'سکشن خالی';

        // تنظیم مقدار status
        $data['status'] = $request->has('status') ? 'active' : 'inactive';

        // ذخیره تصویر اصلی
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '_' . $image->getClientOriginalName();
            $image->move(public_path('uploads/products'), $imageName);
            $data['image'] = '/uploads/products/' . $imageName;
        }
        
        // ذخیره تصاویر دیگر
        if ($request->hasFile('other_image')) {
            $otherImages = [];
            foreach ($request->file('other_image') as $image) {
                $imageName = time() . '_' . $image->getClientOriginalName();
                $image->move(public_path('uploads/products'), $imageName);
                $otherImages[] = '/uploads/products/' . $imageName;
            }
            $data['other_image'] = json_encode($otherImages);
        }

        $product = Product::create($data);

        // ذخیره اطلاعات product_details
        $product->detail()->create([
            'dimension' => $request->input('dimension'),
            'country' => $request->input('country'),
            'suitable_for' => $request->input('suitable_for'),
            'manufacturer' => $request->input('manufacturer'),
            'introduction' => $request->input('introduction'),
            'introduction_video' => $request->input('introduction_video'),
            'pexpline' => $request->input('pexpline'),
        ]);

        // ذخیره گزینه‌های محصول
        if ($request->has('options')) {
            foreach ($request->options as $optionData) {
                if (!empty($optionData['name'])) {
                    ProductOption::create([
                        'product_id' => $product->id,
                        'name' => $optionData['name'],
                        'color_name' => $optionData['color_name'] ?? null,
                        'color_code' => $optionData['color_code'] ?? null,
                        'size' => $optionData['size'] ?? null,
                        'price' => $optionData['price'] ?? 0,
                        'inventory' => $optionData['inventory'] ?? 0,
                        'note' => $optionData['note'] ?? null,
                    ]);
                }
            }
        }

        return redirect()->route('admin.products.index')->with('success', 'محصول با موفقیت افزوده شد');
    }

    public function edit(Product $product)
    {
        $categories = ProductCategory::all();
        return view('admin.products.edit', compact('product', 'categories'));
    }

    public function update(Request $request, Product $product)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric',
            'no_offer_price' => 'nullable|numeric',
            'inventory' => 'required|integer',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'other_image.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'type' => 'required|string|max:255',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string|max:255',
            'is_special' => 'boolean',
            'product_category' => 'required|exists:product_categories,id',
            'badge' => 'nullable|string|max:255',
            'badge_class' => 'nullable|string|max:255',
            'status' => 'boolean',
            'options' => 'nullable|array',
            'options.*.name' => 'nullable|string|max:255',
            'options.*.color_name' => 'nullable|string|max:255',
            'options.*.color_code' => 'nullable|string|max:7',
            'options.*.size' => 'nullable|string|in:s,m,l,xl,2xl,3xl',
            'options.*.price' => 'nullable|numeric|min:0',
            'options.*.inventory' => 'nullable|integer|min:0',
            'options.*.note' => 'nullable|string|max:500',
            'dimension' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'suitable_for' => 'nullable|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'introduction' => 'nullable|string',
            'introduction_video' => 'nullable|string',
            'pexpline' => 'nullable|string',
        ]);

        // تنظیم مقدار status
        $data['status'] = $request->has('status') ? 'active' : 'inactive';

        // آپدیت تصویر اصلی
        if ($request->hasFile('image')) {
            // حذف تصویر قبلی
            if ($product->image && file_exists(public_path($product->image))) {
                unlink(public_path($product->image));
            }
            
            $image = $request->file('image');
            $imageName = time() . '_' . $image->getClientOriginalName();
            $image->move(public_path('uploads/products'), $imageName);
            $data['image'] = '/uploads/products/' . $imageName;
        }

        // آپدیت تصاویر دیگر
        if ($request->hasFile('other_image')) {
            // حذف تصاویر قبلی
            if ($product->other_image) {
                foreach (json_decode($product->other_image) as $oldImage) {
                    if (file_exists(public_path($oldImage))) {
                        unlink(public_path($oldImage));
                    }
                }
            }
            
            $otherImages = [];
            foreach ($request->file('other_image') as $image) {
                $imageName = time() . '_' . $image->getClientOriginalName();
                $image->move(public_path('uploads/products'), $imageName);
                $otherImages[] = '/uploads/products/' . $imageName;
            }
            $data['other_image'] = json_encode($otherImages);
        }

        $product->update($data);

        // ذخیره یا به‌روزرسانی اطلاعات product_details
        $product->detail()->updateOrCreate(
            ['product_id' => $product->id],
            [
                'dimension' => $request->input('dimension'),
                'country' => $request->input('country'),
                'suitable_for' => $request->input('suitable_for'),
                'manufacturer' => $request->input('manufacturer'),
                'introduction' => $request->input('introduction'),
                'introduction_video' => $request->input('introduction_video'),
                'pexpline' => $request->input('pexpline'),
            ]
        );

        // حذف گزینه‌های قبلی
        $product->options()->delete();

        // ذخیره گزینه‌های جدید محصول
        if ($request->has('options')) {
            foreach ($request->options as $optionData) {
                if (!empty($optionData['name'])) {
                    ProductOption::create([
                        'product_id' => $product->id,
                        'name' => $optionData['name'],
                        'color_name' => $optionData['color_name'] ?? null,
                        'color_code' => $optionData['color_code'] ?? null,
                        'size' => $optionData['size'] ?? null,
                        'price' => $optionData['price'] ?? 0,
                        'inventory' => $optionData['inventory'] ?? 0,
                        'note' => $optionData['note'] ?? null,
                    ]);
                }
            }
        }

        return redirect()->route('admin.products.index')->with('success', 'محصول به‌روزرسانی شد');
    }

    public function destroy(Product $product)
    {
        $product->delete();
        return back()->with('success', 'محصول حذف شد');
    }
}
